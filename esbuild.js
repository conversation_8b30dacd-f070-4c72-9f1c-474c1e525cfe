const esbuild = require('esbuild');

const watchMode = process.argv.includes('--watch');

esbuild.build({
  entryPoints: ['./src/extension.ts'],
  bundle: true,
  platform: 'node',
  target: 'node20',
  outfile: './dist/extension.js',
  sourcemap: true,
  external: ['vscode'],  // vscode api غير مضمنة
  watch: watchMode && {
    onRebuild(error, result) {
      if (error) console.error('Build failed:', error);
      else console.log('Build succeeded');
    }
  }
}).then(() => {
  if (!watchMode) console.log('Build completed');
}).catch(() => process.exit(1));
