import esbuild from 'esbuild';

const watchMode = process.argv.includes('--watch');

const buildOptions = {
  entryPoints: ['./src/extension.ts'],
  bundle: true,
  platform: 'node',
  target: 'node20',
  outfile: './dist/extension.js',
  sourcemap: true,
  external: ['vscode']  // vscode api غير مضمنة
};

if (watchMode) {
  buildOptions.watch = {
    onRebuild(error) {
      if (error) console.error('Build failed:', error);
      else console.log('Build succeeded');
    }
  };
}

esbuild.build(buildOptions).then(() => {
  if (!watchMode) console.log('Build completed');
}).catch(() => process.exit(1));
