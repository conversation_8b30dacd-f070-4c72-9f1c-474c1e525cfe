# AI Coder - دليل البدء السريع 🚀

## الإعداد الأولي ⚙️

### 1. تثبيت الإضافة
- افتح VS Code
- اذهب إلى Extensions (`Ctrl+Shift+X`)
- ا<PERSON><PERSON><PERSON> عن "AI Coder"
- اضغط Install

### 2. إعداد مفتاح API
```
Ctrl+Shift+P → AI: Configure API Key
```
- أدخل مفتاح OpenAI API الخاص بك
- يبدأ المفتاح بـ `sk-`

## الميزات الأساسية 🎯

### 🔥 توليد الكود
```
Ctrl+Shift+P → AI: Generate Code
```
**مثال**: "اكتب دالة لحساب المضروب في JavaScript"

### 📖 شرح الكود
1. حدد الكود المراد شرحه
2. كليك يمين → `AI: Explain Code`
3. اقرأ الشرح في النافذة الجانبية

### ⬆️ تحسين الكود
1. ح<PERSON><PERSON> الكود المراد تحسينه
2. `Ctrl+Shift+P` → `AI: Improve Code`
3. حدد نوع التحسين (أداء، قراءة، أمان)

### 💬 إضافة التعليقات
1. حدد الكود
2. كليك يمين → `AI: Add Comments`
3. اختر نوع التعليقات (JSDoc، inline، إلخ)

### 🐛 البحث عن الأخطاء
1. حدد الكود المشكوك فيه
2. `Ctrl+Shift+P` → `AI: Find Bugs`
3. راجع التحليل والحلول المقترحة

### 🧪 توليد الاختبارات
1. حدد الدالة أو الكلاس
2. `Ctrl+Shift+P` → `AI: Generate Tests`
3. حدد نوع الاختبارات المطلوبة

## الإعدادات المهمة ⚙️

### الوصول للإعدادات
```
Ctrl+, → ابحث عن "AI Coder"
```

### الإعدادات الأساسية
- **Model**: اختر النموذج (GPT-3.5, GPT-4, إلخ)
- **Max Tokens**: حدد طول الاستجابة (50-4000)
- **Temperature**: مستوى الإبداع (0-2)

### الإعدادات المتقدمة
- **Language**: لغة الاستجابة (عربي، إنجليزي، تلقائي)
- **Show Preview**: معاينة الكود قبل الإدراج
- **Auto Save**: حفظ تلقائي بعد التوليد
- **Include Context**: تضمين السياق المحيط

## نصائح للاستخدام الأمثل 💡

### 1. كتابة المطالبات الفعالة
- كن محدداً في طلبك
- اذكر لغة البرمجة المطلوبة
- أضف أمثلة إذا أمكن

**مثال جيد**: "اكتب دالة Python لقراءة ملف CSV وإرجاع DataFrame مع معالجة الأخطاء"

### 2. استخدام السياق
- فعل "Include Context" للحصول على نتائج أفضل
- حدد الكود ذي الصلة قبل الطلب

### 3. مراجعة النتائج
- استخدم "Show Preview" لمراجعة الكود
- تأكد من فهم الكود قبل الاستخدام
- اختبر الكود المولد

## اختصارات مفيدة ⌨️

| الوظيفة | الاختصار |
|---------|----------|
| فتح Command Palette | `Ctrl+Shift+P` |
| إعداد مفتاح API | `Ctrl+Shift+P` → Configure API Key |
| توليد كود | `Ctrl+Shift+P` → Generate Code |
| شرح كود محدد | كليك يمين → Explain Code |
| تحسين كود محدد | كليك يمين → Improve Code |

## حل المشاكل الشائعة 🔧

### مفتاح API لا يعمل
- تأكد من صحة المفتاح
- تحقق من رصيد حسابك في OpenAI
- أعد إدخال المفتاح

### الاستجابة بطيئة
- قلل عدد Max Tokens
- استخدم نموذج أسرع (GPT-3.5)
- تحقق من اتصال الإنترنت

### نتائج غير مرضية
- حسن صياغة المطالب
- فعل Include Context
- جرب نماذج مختلفة

## الدعم والمساعدة 📞

- **GitHub**: [رابط المشروع]
- **Issues**: لتقرير المشاكل
- **Discussions**: للأسئلة والاقتراحات

---

**نصيحة**: ابدأ بالميزات الأساسية ثم انتقل للمتقدمة تدريجياً! 🎯
