# AI Coder Extension 🤖

AI Coder is a powerful Visual Studio Code extension that helps generate code snippets using OpenAI's latest AI technology with Arabic language support.

## ✨ Features

- **🔥 Modern AI Integration**: Uses OpenAI's latest Chat Completions API
- **🔒 Secure API Key Storage**: Safe storage of your OpenAI API key
- **🌍 Arabic Language Support**: Full Arabic interface and prompts
- **⚙️ Customizable Settings**: Configure model, temperature, and token limits
- **📊 Progress Indicators**: Real-time feedback during code generation
- **🎯 Smart Error Handling**: Detailed error messages and suggestions
- **🚀 Multiple AI Models**: Support for GPT-3.5, GPT-4, and latest models

## 🚀 How to Use

### First Time Setup
1. Open the command palette (`Ctrl+Shift+P` or `Cmd+Shift+P`)
2. Type `AI: Configure API Key` and press Enter
3. Enter your OpenAI API key (starts with `sk-`)

### Generate Code
1. Open any file in VS Code
2. Place your cursor where you want to insert code
3. Open command palette (`Ctrl+Shift+P` or `Cmd+Shift+P`)
4. Type `AI: Generate Code` and press Enter
5. Enter your prompt in Arabic or English
6. Wait for the AI to generate and insert the code

## ⚙️ Configuration

You can customize the extension behavior in VS Code settings:

- **Model**: Choose from GPT-3.5-turbo, GPT-4, GPT-4o, etc.
- **Max Tokens**: Control the length of generated code (50-4000)
- **Temperature**: Adjust creativity level (0-2)

## 📋 Requirements

- Visual Studio Code version 1.100.0 or higher
- OpenAI API key (get one at [OpenAI Platform](https://platform.openai.com/))
- Internet connection for AI code generation

## Contributing

Feel free to open issues or submit pull requests to improve the extension.

## License

This project is licensed under the MIT License. See the LICENSE file for details.
