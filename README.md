# AI Coder Extension 🤖

AI Coder is a powerful Visual Studio Code extension that helps generate code snippets using OpenAI's latest AI technology with Arabic language support.

## ✨ Features

### 🤖 AI-Powered Code Operations

- **🔥 Code Generation**: Create new code from natural language descriptions
- **📖 Code Explanation**: Get detailed explanations of existing code
- **⬆️ Code Improvement**: Enhance performance, readability, and security
- **💬 Smart Comments**: Add comprehensive documentation and comments
- **🐛 Bug Detection**: Find potential issues and logic problems
- **🧪 Test Generation**: Create comprehensive unit and integration tests

### 🎯 User Experience

- **🔒 Secure API Key Storage**: Safe storage of your OpenAI API key
- **🌍 Arabic Language Support**: Full Arabic interface and prompts
- **👁️ Code Preview**: Preview generated code before insertion
- **📋 Copy to Clipboard**: Easy code copying functionality
- **🎛️ Context Menu Integration**: Right-click on selected code for quick actions
- **📊 Progress Indicators**: Real-time feedback during AI processing

### ⚙️ Advanced Configuration

- **🚀 Multiple AI Models**: Support for GPT-3.5, GPT-4, GPT-4o, and latest models
- **🌐 Language Settings**: Choose between Arabic, English, or auto-detection
- **🔧 Customizable Parameters**: Configure model, temperature, and token limits
- **💾 Auto-Save**: Automatically save files after code generation
- **🎯 Context Inclusion**: Include surrounding code for better AI understanding

## 🚀 How to Use

### First Time Setup

1. Open the command palette (`Ctrl+Shift+P` or `Cmd+Shift+P`)
2. Type `AI: Configure API Key` and press Enter
3. Enter your OpenAI API key (starts with `sk-`)

### Generate Code

1. Open any file in VS Code
2. Place your cursor where you want to insert code
3. Open command palette (`Ctrl+Shift+P` or `Cmd+Shift+P`)
4. Type `AI: Generate Code` and press Enter
5. Enter your prompt in Arabic or English
6. Wait for the AI to generate and insert the code

### Using Other AI Features

#### 📖 Explain Code

1. Select the code you want to understand
2. Right-click and choose `AI: Explain Code`
3. View the detailed explanation in a side panel

#### ⬆️ Improve Code

1. Select the code you want to enhance
2. Use `Ctrl+Shift+P` → `AI: Improve Code`
3. Specify the type of improvement needed
4. Review and apply the suggestions

#### 💬 Add Comments

1. Select the code that needs documentation
2. Right-click and choose `AI: Add Comments`
3. Choose comment style (JSDoc, inline, etc.)
4. Preview and insert the comments

#### 🐛 Find Bugs

1. Select problematic code
2. Use `AI: Find Bugs` command
3. Review the analysis and suggested fixes

#### 🧪 Generate Tests

1. Select the function/class to test
2. Use `AI: Generate Tests` command
3. Specify test type and framework
4. Review and add the generated tests

## ⚙️ Configuration

You can customize the extension behavior in VS Code settings:

### Basic Settings

- **Model**: Choose from GPT-3.5-turbo, GPT-4, GPT-4o, etc.
- **Max Tokens**: Control the length of generated code (50-4000)
- **Temperature**: Adjust creativity level (0-2)

### Advanced Settings

- **Language**: Set response language (Arabic, English, Auto)
- **Show Preview**: Enable/disable code preview before insertion
- **Auto Save**: Automatically save files after code generation
- **Include Context**: Include surrounding code for better AI understanding

### Access Settings

1. Open VS Code Settings (`Ctrl+,` or `Cmd+,`)
2. Search for "AI Coder"
3. Adjust settings as needed

## 📋 Requirements

- Visual Studio Code version 1.100.0 or higher
- OpenAI API key (get one at [OpenAI Platform](https://platform.openai.com/))
- Internet connection for AI code generation

## Contributing

Feel free to open issues or submit pull requests to improve the extension.

## License

This project is licensed under the MIT License. See the LICENSE file for details.
