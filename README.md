# AI Coder Extension

AI Coder is a Visual Studio Code extension that helps generate code snippets using AI technology.

## Features

- Generate code snippets based on your instructions.
- Supports multiple programming languages.
- Easy to use command palette integration.

## How to Use

1. Open the command palette (Ctrl+Shift+P or Cmd+Shift+P).
2. Type `AI: Generate Code` and press Enter.
3. Enter your prompt or instructions.
4. The extension will generate code based on your input.

## Installation

- Install directly from the Visual Studio Code Marketplace.
- Or clone the repository and run the extension locally.

## Requirements

- Visual Studio Code version 1.100.0 or higher.
- Internet connection for AI code generation.

## Contributing

Feel free to open issues or submit pull requests to improve the extension.

## License

This project is licensed under the MIT License. See the LICENSE file for details.
