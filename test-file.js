// ملف اختبار لتجربة إضافة AI Coder

function calculateSum(a, b) {
    return a + b;
}

// دالة معقدة تحتاج تحسين
function processData(data) {
    var result = [];
    for (var i = 0; i < data.length; i++) {
        if (data[i] != null) {
            if (data[i] > 0) {
                result.push(data[i] * 2);
            }
        }
    }
    return result;
}

// كود يحتوي على مشكلة محتملة
function divideNumbers(x, y) {
    return x / y; // لا يوجد فحص للقسمة على صفر
}

console.log("اختبار الإضافة");
هناك عدة طرق لحساب المضروب، سأقدم ثلاث طرق مختلفة مع تعليقات باللغة العربية:

**الطريقة الأولى: باستخدام حلقة `for`**

هذه الطريقة بسيطة وسهلة الفهم، مناسبة للمبتدئين.

```python
def factorial_iterative(n):
  """
  دالة لحساب مضروب عدد صحيح باستخدام حلقة for.

  Args:
    n: عدد صحيح موجب.

  Returns:
    مضروب n.  يرجع 1 إذا كان n يساوي 0.  يرجع خطأ إذا كان n سالب.
  """
  if n < 0:
    raise ValueError("لا يمكن حساب مضروب عدد سالب")  # معالجة حالات الخطأ
  elif n == 0:
    return 1  # حالة قاعدة المضروب
  else:
    result = 1
    for i in range(1, n + 1):
      result *= i
    return result

# مثال على استخدام الدالة
print(factorial_iterative(5))  # ينتج 120
print(factorial_iterative(0))  # ينتج 1
#print(factorial_iterative(-5)) # يسبب خطأ
```

**الطريقة الثانية: باستخدام دالة متكررة (Recursive)**

هذه الطريقة أنيقة و تعتمد على تعريف المضروب نفسه، لكنها قد تكون أقل كفاءة للحجج الكبيرة بسبب استدعاءات الدالة المتكررة.

```python
def factorial_recursive(n):
  """
  دالة لحساب مضروب عدد صحيح باستخدام الدالة المتكررة (recursive).

  Args:
    n: عدد صحيح موجب.

  Returns:
    مضروب n. يرجع 1 إذا كان n يساوي 0. يرجع خطأ إذا كان n سالب.
  """
  if n < 0:
    raise ValueError("لا يمكن حساب مضروب عدد سالب") # معالجة حالات الخطأ
  elif n == 0:
    return 1 # حالة قاعدة المضروب
  else:
    return n * factorial_recursive(n - 1)

# مثال على استخدام الدالة
print(factorial_recursive(5))  # ينتج