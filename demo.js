// ملف اختبار لتجربة إضافة AI Coder

function calculateSum(a, b) {
    return a + b;
}

// دالة معقدة تحتاج تحسين
function processData(data) {
    var result = [];
    for (var i = 0; i < data.length; i++) {
        if (data[i] != null) {
            if (data[i] > 0) {
                result.push(data[i] * 2);
            }
        }
    }
    return result;
}

// كود يحتوي على مشكلة محتملة
function divideNumbers(x, y) {
    return x / y; // لا يوجد فحص للقسمة على صفر
}

console.log("اختبار الإضافة");
