<!DOCTYPE html>
<html>
<head>
    <title>Atayer Icon Generator</title>
</head>
<body>
    <canvas id="canvas" width="128" height="128"></canvas>
    <br><br>
    <button onclick="downloadIcon()">تحميل الأيقونة</button>

    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');

        // Background gradient - dark tech theme
        const gradient = ctx.createLinearGradient(0, 0, 128, 128);
        gradient.addColorStop(0, '#1a1a2e');
        gradient.addColorStop(0.5, '#16213e');
        gradient.addColorStop(1, '#0f3460');

        // Draw background circle
        ctx.fillStyle = gradient;
        ctx.beginPath();
        ctx.arc(64, 64, 60, 0, 2 * Math.PI);
        ctx.fill();

        // Draw border with glow effect
        ctx.strokeStyle = '#00d4ff';
        ctx.lineWidth = 3;
        ctx.shadowColor = '#00d4ff';
        ctx.shadowBlur = 10;
        ctx.stroke();
        ctx.shadowBlur = 0;

        // Draw Stealth Fighter shape
        ctx.fillStyle = '#ffffff';
        ctx.strokeStyle = '#00d4ff';
        ctx.lineWidth = 2;

        // Main body of stealth fighter
        ctx.beginPath();
        // Nose (front point)
        ctx.moveTo(64, 25);
        // Right wing
        ctx.lineTo(95, 55);
        ctx.lineTo(90, 70);
        ctx.lineTo(75, 75);
        // Right tail
        ctx.lineTo(80, 90);
        ctx.lineTo(70, 95);
        // Center back
        ctx.lineTo(64, 85);
        // Left tail
        ctx.lineTo(58, 95);
        ctx.lineTo(48, 90);
        // Left wing
        ctx.lineTo(53, 75);
        ctx.lineTo(38, 70);
        ctx.lineTo(33, 55);
        // Back to nose
        ctx.closePath();

        // Fill and stroke the stealth fighter
        ctx.fill();
        ctx.stroke();

        // Add cockpit detail
        ctx.fillStyle = '#00d4ff';
        ctx.beginPath();
        ctx.ellipse(64, 45, 8, 12, 0, 0, 2 * Math.PI);
        ctx.fill();

        // Add engine exhausts
        ctx.fillStyle = '#ff6b6b';
        ctx.beginPath();
        ctx.ellipse(58, 85, 3, 6, 0, 0, 2 * Math.PI);
        ctx.fill();
        ctx.beginPath();
        ctx.ellipse(70, 85, 3, 6, 0, 0, 2 * Math.PI);
        ctx.fill();

        // Add tech lines on wings
        ctx.strokeStyle = '#00d4ff';
        ctx.lineWidth = 1;
        ctx.globalAlpha = 0.7;

        // Right wing lines
        ctx.beginPath();
        ctx.moveTo(75, 50);
        ctx.lineTo(85, 60);
        ctx.stroke();

        ctx.beginPath();
        ctx.moveTo(78, 55);
        ctx.lineTo(88, 65);
        ctx.stroke();

        // Left wing lines
        ctx.beginPath();
        ctx.moveTo(53, 50);
        ctx.lineTo(43, 60);
        ctx.stroke();

        ctx.beginPath();
        ctx.moveTo(50, 55);
        ctx.lineTo(40, 65);
        ctx.stroke();

        // Arabic text "أطاير" with glow
        ctx.globalAlpha = 1;
        ctx.fillStyle = '#ffffff';
        ctx.font = 'bold 14px Arial';
        ctx.textAlign = 'center';
        ctx.shadowColor = '#00d4ff';
        ctx.shadowBlur = 5;
        ctx.fillText('أطاير', 64, 115);

        // AI text
        ctx.shadowBlur = 0;
        ctx.font = 'bold 12px monospace';
        ctx.fillStyle = '#00d4ff';
        ctx.fillText('AI', 64, 15);

        function downloadIcon() {
            const link = document.createElement('a');
            link.download = 'icon.png';
            link.href = canvas.toDataURL();
            link.click();
        }
    </script>
</body>
</html>
