<!DOCTYPE html>
<html>
<head>
    <title>Atayer Icon Generator</title>
</head>
<body>
    <canvas id="canvas" width="128" height="128"></canvas>
    <br><br>
    <button onclick="downloadIcon()">تحميل الأيقونة</button>
    
    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        
        // Background gradient
        const gradient = ctx.createLinearGradient(0, 0, 128, 128);
        gradient.addColorStop(0, '#667eea');
        gradient.addColorStop(1, '#764ba2');
        
        // Draw background circle
        ctx.fillStyle = gradient;
        ctx.beginPath();
        ctx.arc(64, 64, 60, 0, 2 * Math.PI);
        ctx.fill();
        
        // Draw border
        ctx.strokeStyle = '#ffffff';
        ctx.lineWidth = 4;
        ctx.stroke();
        
        // Draw AI brain/circuit pattern
        ctx.strokeStyle = '#ffffff';
        ctx.lineWidth = 2;
        ctx.globalAlpha = 0.8;
        
        // Central node
        ctx.fillStyle = '#ffffff';
        ctx.beginPath();
        ctx.arc(64, 64, 8, 0, 2 * Math.PI);
        ctx.fill();
        
        // Connection lines
        const connections = [
            [64, 64, 40, 40],
            [64, 64, 88, 40],
            [64, 64, 40, 88],
            [64, 64, 88, 88],
            [64, 64, 30, 64],
            [64, 64, 98, 64],
            [64, 64, 64, 30],
            [64, 64, 64, 98]
        ];
        
        connections.forEach(([x1, y1, x2, y2]) => {
            ctx.beginPath();
            ctx.moveTo(x1, y1);
            ctx.lineTo(x2, y2);
            ctx.stroke();
            
            // Small nodes at endpoints
            ctx.fillStyle = '#ffffff';
            ctx.beginPath();
            ctx.arc(x2, y2, 3, 0, 2 * Math.PI);
            ctx.fill();
        });
        
        // Arabic text "أطاير"
        ctx.globalAlpha = 1;
        ctx.fillStyle = '#ffffff';
        ctx.font = 'bold 16px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('أطاير', 64, 110);
        
        // Code brackets
        ctx.font = 'bold 20px monospace';
        ctx.fillText('{ }', 64, 25);
        
        function downloadIcon() {
            const link = document.createElement('a');
            link.download = 'icon.png';
            link.href = canvas.toDataURL();
            link.click();
        }
    </script>
</body>
</html>
