import * as assert from 'assert';
import * as vscode from 'vscode';

suite('AI Coder Extension Test Suite', () => {
	vscode.window.showInformationMessage('بدء اختبارات إضافة AI Coder');

	test('Extension should be present', () => {
		const extension = vscode.extensions.getExtension('ai-coder');
		assert.ok(extension, 'Extension should be found');
	});

	test('Commands should be registered', async () => {
		const commands = await vscode.commands.getCommands(true);

		const expectedCommands = [
			'aiCoder.generate',
			'aiCoder.explainCode',
			'aiCoder.improveCode',
			'aiCoder.addComments',
			'aiCoder.findBugs',
			'aiCoder.generateTests',
			'aiCoder.configure',
			'aiCoder.getStoredApiKey',
			'aiCoder.testConnection'
		];

		expectedCommands.forEach(command => {
			assert.ok(
				commands.includes(command),
				`${command} command should be registered`
			);
		});
	});

	test('Configuration properties should exist', () => {
		const config = vscode.workspace.getConfiguration('aiCoder');

		// Test basic settings default values
		assert.strictEqual(config.get('provider'), 'gemini');
		assert.strictEqual(config.get('model'), 'gemini-1.5-flash');
		assert.strictEqual(config.get('maxTokens'), 500);
		assert.strictEqual(config.get('temperature'), 0.7);

		// Test new settings default values
		assert.strictEqual(config.get('language'), 'arabic');
		assert.strictEqual(config.get('showPreview'), true);
		assert.strictEqual(config.get('autoSave'), false);
		assert.strictEqual(config.get('includeContext'), true);
	});

	test('API Key storage and retrieval', async () => {
		// This test would need to mock the global state
		// For now, we just test that the command exists
		const commands = await vscode.commands.getCommands(true);
		assert.ok(commands.includes('aiCoder.getStoredApiKey'));
	});
});
