import * as assert from 'assert';
import * as vscode from 'vscode';

suite('AI Coder Extension Test Suite', () => {
	vscode.window.showInformationMessage('بدء اختبارات إضافة AI Coder');

	test('Extension should be present', () => {
		const extension = vscode.extensions.getExtension('ai-coder');
		assert.ok(extension, 'Extension should be found');
	});

	test('Commands should be registered', async () => {
		const commands = await vscode.commands.getCommands(true);

		assert.ok(
			commands.includes('aiCoder.generate'),
			'aiCoder.generate command should be registered'
		);

		assert.ok(
			commands.includes('aiCoder.configure'),
			'aiCoder.configure command should be registered'
		);

		assert.ok(
			commands.includes('aiCoder.getStoredApiKey'),
			'aiCoder.getStoredApiKey command should be registered'
		);
	});

	test('Configuration properties should exist', () => {
		const config = vscode.workspace.getConfiguration('aiCoder');

		// Test default values
		assert.strictEqual(config.get('model'), 'gpt-3.5-turbo');
		assert.strictEqual(config.get('maxTokens'), 500);
		assert.strictEqual(config.get('temperature'), 0.7);
	});

	test('API Key storage and retrieval', async () => {
		// This test would need to mock the global state
		// For now, we just test that the command exists
		const commands = await vscode.commands.getCommands(true);
		assert.ok(commands.includes('aiCoder.getStoredApiKey'));
	});
});
