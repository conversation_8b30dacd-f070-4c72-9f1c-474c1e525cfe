# Development Guide 🛠️

This guide will help you set up the development environment and contribute to the AI Coder extension.

## Prerequisites

- Node.js (v18 or higher)
- Visual Studio Code
- Git

## Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd ai-coder
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Build the extension**
   ```bash
   npm run compile
   ```

## Development Workflow

### Building
- **Development build**: `npm run compile`
- **Watch mode**: `npm run watch` (rebuilds on file changes)

### Testing
- **Run tests**: Open VS Code, press `F5` to launch Extension Development Host
- **Run unit tests**: Use the Test Explorer in VS Code

### Debugging
1. Open the project in VS Code
2. Press `F5` to launch Extension Development Host
3. Set breakpoints in your TypeScript code
4. Test the extension in the new VS Code window

## Project Structure

```
├── src/
│   ├── extension.ts          # Main extension code
│   └── test/
│       └── extension.test.ts # Test files
├── dist/                     # Compiled JavaScript
├── package.json              # Extension manifest
├── tsconfig.json            # TypeScript configuration
├── esbuild.js               # Build configuration
└── README.md                # Documentation
```

## Key Components

### Extension Activation
- Entry point: `src/extension.ts`
- Exports `activate()` and `deactivate()` functions
- Registers commands and configuration

### Commands
- `aiCoder.generate`: Main code generation command
- `aiCoder.configure`: API key configuration
- `aiCoder.getStoredApiKey`: Internal helper command

### Configuration
- Settings defined in `package.json` under `contributes.configuration`
- Accessed via `vscode.workspace.getConfiguration('aiCoder')`

## API Integration

### OpenAI Chat Completions
- Endpoint: `https://api.openai.com/v1/chat/completions`
- Authentication: Bearer token
- Models supported: GPT-3.5, GPT-4, GPT-4o, etc.

### Error Handling
- Network errors
- API authentication errors
- Rate limiting
- Invalid responses

## Security Considerations

- API keys stored in VSCode's global state (encrypted)
- No hardcoded credentials in source code
- Input validation for user prompts
- Secure HTTP requests only

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## Release Process

1. Update version in `package.json`
2. Update `CHANGELOG.md`
3. Build and test
4. Create release tag
5. Publish to VS Code Marketplace

## Troubleshooting

### Build Issues
- Ensure Node.js version compatibility
- Clear `node_modules` and reinstall
- Check TypeScript compilation errors

### Extension Not Loading
- Check VS Code version compatibility
- Verify `package.json` manifest
- Check console for activation errors

### API Issues
- Verify API key configuration
- Check network connectivity
- Review OpenAI API status

## Resources

- [VS Code Extension API](https://code.visualstudio.com/api)
- [OpenAI API Documentation](https://platform.openai.com/docs)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
