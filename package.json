{"name": "ai-coder", "displayName": "AI Coder", "description": "VSCode extension to generate code snippets with OpenAI", "version": "0.0.1", "engines": {"vscode": "^1.100.0"}, "categories": ["Other"], "activationEvents": ["onCommand:aiCoder.generate"], "main": "./dist/extension.js", "contributes": {"commands": [{"command": "aiCoder.generate", "title": "AI: Generate Code"}]}, "scripts": {"compile": "node esbuild.js", "watch": "node esbuild.js --watch", "package": "vsce package"}, "devDependencies": {"@types/mocha": "^10.0.10", "@types/node": "^20.0.0", "@types/node-fetch": "^2.6.12", "@types/vscode": "^1.100.0", "esbuild": "^0.25.3", "typescript": "^5.8.3"}, "dependencies": {"node-fetch": "^2.7.0"}}