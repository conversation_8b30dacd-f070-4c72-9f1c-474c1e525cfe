{"name": "atayer", "displayName": "<PERSON><PERSON><PERSON> - AI Code Assistant", "description": "Atayer - Your intelligent Arabic AI coding assistant. Supports Google Gemini & OpenAI for code generation, explanation, improvement, and more with full Arabic interface", "version": "1.0.1", "engines": {"vscode": "^1.100.0"}, "categories": ["Other", "Machine Learning", "Snippets"], "keywords": ["atayer", "ai", "artificial intelligence", "code generation", "gemini", "openai", "arabic", "عربي", "programming assistant", "code explanation", "code improvement", "مسا<PERSON><PERSON> برمجة"], "author": {"name": "<PERSON>"}, "publisher": "hussein-ai-coder", "license": "MIT", "type": "module", "repository": {"type": "git", "url": "https://github.com/hussein/atayer"}, "bugs": {"url": "https://github.com/hussein/atayer/issues"}, "homepage": "https://github.com/hussein/atayer#readme", "main": "./dist/extension.js", "activationEvents": ["onStartupFinished"], "contributes": {"commands": [{"command": "aiCoder.generate", "title": "AI: Generate Code", "icon": "$(sparkle)"}, {"command": "aiCoder.explainCode", "title": "AI: Explain Code", "icon": "$(question)"}, {"command": "aiCoder.improveCode", "title": "AI: Improve Code", "icon": "$(arrow-up)"}, {"command": "aiCoder.addComments", "title": "AI: Add Comments", "icon": "$(comment)"}, {"command": "aiCoder.findBugs", "title": "AI: <PERSON>", "icon": "$(bug)"}, {"command": "aiCoder.generateTests", "title": "AI: Generate Tests", "icon": "$(beaker)"}, {"command": "aiCoder.configure", "title": "AI: Configure API Key", "icon": "$(gear)"}, {"command": "aiCoder.testConnection", "title": "AI: Test Connection", "icon": "$(plug)"}], "menus": {"editor/context": [{"when": "editorHasSelection", "command": "aiCoder.explainCode", "group": "aiCoder@1"}, {"when": "editorHasSelection", "command": "aiCoder.improveCode", "group": "aiCoder@2"}, {"when": "editorHasSelection", "command": "aiCoder.addComments", "group": "aiCoder@3"}, {"when": "editorHasSelection", "command": "aiCoder.findBugs", "group": "aiCoder@4"}]}, "configuration": {"title": "AI Coder", "properties": {"aiCoder.provider": {"type": "string", "default": "gemini", "enum": ["openai", "gemini"], "description": "مزو<PERSON> خدمة الذكاء الاصطناعي"}, "aiCoder.model": {"type": "string", "default": "gemini-1.5-flash", "enum": ["gpt-3.5-turbo", "gpt-4", "gpt-4-turbo-preview", "gpt-4o", "gpt-4o-mini", "gemini-1.5-flash", "gemini-1.5-pro", "gemini-pro"], "description": "النموذج المستخدم لتوليد الكود"}, "aiCoder.maxTokens": {"type": "number", "default": 500, "minimum": 50, "maximum": 4000, "description": "الح<PERSON> الأق<PERSON>ى لعدد الرموز في الاستجابة"}, "aiCoder.temperature": {"type": "number", "default": 0.7, "minimum": 0, "maximum": 2, "description": "درجة الإبداع في الاستجابة (0 = محافظ، 2 = مبدع)"}, "aiCoder.language": {"type": "string", "default": "arabic", "enum": ["arabic", "english", "auto"], "description": "لغة الاستجابة والتعليقات"}, "aiCoder.showPreview": {"type": "boolean", "default": true, "description": "عرض معاينة الكود قبل الإدراج"}, "aiCoder.autoSave": {"type": "boolean", "default": false, "description": "حفظ الملف تلقائياً بعد توليد الكود"}, "aiCoder.includeContext": {"type": "boolean", "default": true, "description": "تضمين سياق الكود المحيط في الطلب"}}}}, "scripts": {"compile": "node esbuild.js", "watch": "node esbuild.js --watch", "package": "vsce package"}, "devDependencies": {"@types/mocha": "^10.0.10", "@types/node": "^20.0.0", "@types/node-fetch": "^2.6.12", "@types/vscode": "^1.100.0", "esbuild": "^0.25.3", "typescript": "^5.8.3"}, "dependencies": {"node-fetch": "^2.7.0"}}