{"name": "ai-coder", "displayName": "AI Coder", "description": "VSCode extension to generate code snippets with OpenAI", "version": "0.1.0", "engines": {"vscode": "^1.100.0"}, "categories": ["Other"], "main": "./dist/extension.js", "contributes": {"commands": [{"command": "aiCoder.generate", "title": "AI: Generate Code", "icon": "$(sparkle)"}, {"command": "aiCoder.configure", "title": "AI: Configure API Key", "icon": "$(gear)"}], "configuration": {"title": "AI Coder", "properties": {"aiCoder.model": {"type": "string", "default": "gpt-3.5-turbo", "enum": ["gpt-3.5-turbo", "gpt-4", "gpt-4-turbo-preview", "gpt-4o", "gpt-4o-mini"], "description": "نموذج OpenAI المستخدم لتوليد الكود"}, "aiCoder.maxTokens": {"type": "number", "default": 500, "minimum": 50, "maximum": 4000, "description": "الح<PERSON> الأق<PERSON>ى لعدد الرموز في الاستجابة"}, "aiCoder.temperature": {"type": "number", "default": 0.7, "minimum": 0, "maximum": 2, "description": "درجة الإبداع في الاستجابة (0 = محافظ، 2 = مبدع)"}}}}, "scripts": {"compile": "node esbuild.js", "watch": "node esbuild.js --watch", "package": "vsce package"}, "devDependencies": {"@types/mocha": "^10.0.10", "@types/node": "^20.0.0", "@types/node-fetch": "^2.6.12", "@types/vscode": "^1.100.0", "esbuild": "^0.25.3", "typescript": "^5.8.3"}, "dependencies": {"node-fetch": "^2.7.0"}}