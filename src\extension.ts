import * as vscode from 'vscode';
import fetch from 'node-fetch';

export function activate(context: vscode.ExtensionContext) {
  console.log('AI Coder extension activated');

  const disposable = vscode.commands.registerCommand('aiCoder.generate', async () => {
    const editor = vscode.window.activeTextEditor;
    if (!editor) {
      vscode.window.showInformationMessage('لا يوجد ملف مفتوح.');
      return;
    }

    const userPrompt = await vscode.window.showInputBox({
      prompt: 'ما الذي تود أن يولّده الذكاء الاصطناعي؟'
    });
    if (!userPrompt) return;

    try {
      const response = await fetch('https://api.openai.com/v1/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `t6ouYF3LMoGyCqhQb4VmMObo0SlVditIUDHy0wxgT6PdiHT4Dj930oWRhywWXk6Aq0xpnMAjQ7T3BlbkFJIrdK6jDnqMas9BoUReBignlJdcwJUmcqdNm_eJmMLSAyD4ss6jsRTHbNCiDaoLUGsqFNkvP5YA`
        },
        body: JSON.stringify({
          model: 'text-davinci-003',
          prompt: userPrompt,
          max_tokens: 150,
          temperature: 0.7
        })
      });

      if (!response.ok) {
        vscode.window.showErrorMessage(`خطأ من الخادم: ${response.statusText}`);
        return;
      }

      const data = await response.json();
      const generated = data.choices?.[0]?.text?.trim();

      if (generated) {
        editor.edit(editBuilder => {
          editBuilder.insert(editor.selection.active, generated);
        });
      } else {
        vscode.window.showErrorMessage('لم يتم توليد أي كود.');
      }
    } catch (error) {
      vscode.window.showErrorMessage('حدث خطأ أثناء الاتصال بالذكاء الاصطناعي.');
      console.error(error);
    }
  });

  context.subscriptions.push(disposable);
}

export function deactivate() {
  console.log('AI Coder extension deactivated');
}
