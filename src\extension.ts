import * as vscode from 'vscode';
import fetch from 'node-fetch';

interface OpenAIResponse {
  choices: Array<{
    message: {
      content: string;
    };
  }>;
  error?: {
    message: string;
    type: string;
  };
}

interface ExtensionConfig {
  apiKey: string;
  provider: string;
  model: string;
  maxTokens: number;
  temperature: number;
  language: string;
  showPreview: boolean;
  autoSave: boolean;
  includeContext: boolean;
}

enum AIAction {
  GENERATE = 'generate',
  EXPLAIN = 'explain',
  IMPROVE = 'improve',
  COMMENT = 'comment',
  DEBUG = 'debug',
  TEST = 'test'
}

export function activate(context: vscode.ExtensionContext) {
  console.log('AI Coder extension activated');

  // أمر مساعد للحصول على مفتاح API المحفوظ
  const getStoredApiKeyCommand = vscode.commands.registerCommand('aiCoder.getStoredApiKey', () => {
    return context.globalState.get<string>('aiCoder.apiKey');
  });

  const disposable = vscode.commands.registerCommand('aiCoder.generate', async () => {
    await handleAIRequest(AIAction.GENERATE);
  });

  // إضافة أمر لإعداد مفتاح API
  const configureCommand = vscode.commands.registerCommand('aiCoder.configure', async () => {
    const config = vscode.workspace.getConfiguration('aiCoder');
    const currentProvider = config.get<string>('provider') || 'gemini';

    let prompt: string;
    let placeholder: string;

    if (currentProvider === 'gemini') {
      prompt = 'أدخل مفتاح Google Gemini API الخاص بك';
      placeholder = 'AIza... (احصل عليه من Google AI Studio)';
    } else {
      prompt = 'أدخل مفتاح OpenAI API الخاص بك';
      placeholder = 'sk-... (احصل عليه من OpenAI Platform)';
    }

    const apiKey = await vscode.window.showInputBox({
      prompt,
      placeHolder: placeholder,
      password: true,
      ignoreFocusOut: true
    });

    if (apiKey) {
      await context.globalState.update('aiCoder.apiKey', apiKey);
      vscode.window.showInformationMessage(`تم حفظ مفتاح ${currentProvider.toUpperCase()} API بنجاح! ✅`);
    }
  });

  // أمر شرح الكود
  const explainCommand = vscode.commands.registerCommand('aiCoder.explainCode', async () => {
    await handleAIRequest(AIAction.EXPLAIN);
  });

  // أمر تحسين الكود
  const improveCommand = vscode.commands.registerCommand('aiCoder.improveCode', async () => {
    await handleAIRequest(AIAction.IMPROVE);
  });

  // أمر إضافة التعليقات
  const commentCommand = vscode.commands.registerCommand('aiCoder.addComments', async () => {
    await handleAIRequest(AIAction.COMMENT);
  });

  // أمر البحث عن الأخطاء
  const debugCommand = vscode.commands.registerCommand('aiCoder.findBugs', async () => {
    await handleAIRequest(AIAction.DEBUG);
  });

  // أمر توليد الاختبارات
  const testCommand = vscode.commands.registerCommand('aiCoder.generateTests', async () => {
    await handleAIRequest(AIAction.TEST);
  });

  // أمر اختبار الاتصال
  const testConnectionCommand = vscode.commands.registerCommand('aiCoder.testConnection', async () => {
    const config = await getConfiguration();
    if (!config) return;

    if (config.provider === 'gemini') {
      await testGeminiConnection(config.apiKey);
    } else {
      vscode.window.showInformationMessage('اختبار الاتصال متاح فقط لـ Gemini حالياً');
    }
  });

  context.subscriptions.push(
    disposable,
    configureCommand,
    getStoredApiKeyCommand,
    explainCommand,
    improveCommand,
    commentCommand,
    debugCommand,
    testCommand,
    testConnectionCommand
  );
}

async function handleAIRequest(action: AIAction, customPrompt?: string): Promise<void> {
  const editor = vscode.window.activeTextEditor;
  if (!editor) {
    vscode.window.showInformationMessage('لا يوجد ملف مفتوح.');
    return;
  }

  const config = await getConfiguration();
  if (!config) return;

  let selectedText = '';
  let userPrompt = customPrompt || '';

  // الحصول على النص المحدد أو السياق
  if (action !== AIAction.GENERATE) {
    const selection = editor.selection;
    if (!selection.isEmpty) {
      selectedText = editor.document.getText(selection);
    } else if (config.includeContext) {
      // الحصول على السياق المحيط
      const line = editor.selection.active.line;
      const startLine = Math.max(0, line - 5);
      const endLine = Math.min(editor.document.lineCount - 1, line + 5);
      const range = new vscode.Range(startLine, 0, endLine, editor.document.lineAt(endLine).text.length);
      selectedText = editor.document.getText(range);
    }
  }

  // إذا لم يكن هناك prompt مخصص، اطلب من المستخدم
  if (!userPrompt) {
    const prompt = await getUserPrompt(action);
    if (!prompt) return;
    userPrompt = prompt;
  }

  const systemPrompt = getSystemPrompt(action, config.language);
  const fullPrompt = buildFullPrompt(action, userPrompt, selectedText, editor.document.languageId);

  await processAIRequest(config, systemPrompt, fullPrompt, action, editor);
}

async function getConfiguration(): Promise<ExtensionConfig | null> {
  const config = vscode.workspace.getConfiguration('aiCoder');

  // الحصول على مفتاح API من التخزين الآمن
  let apiKey = await vscode.commands.executeCommand('aiCoder.getStoredApiKey') as string;

  if (!apiKey) {
    // محاولة الحصول على المفتاح من الإعدادات العامة (للتوافق مع الإصدارات القديمة)
    apiKey = config.get<string>('apiKey') || '';
  }

  if (!apiKey) {
    const provider = config.get<string>('provider') || 'gemini';
    const providerName = provider === 'gemini' ? 'Google Gemini' : 'OpenAI';

    const action = await vscode.window.showErrorMessage(
      `لم يتم العثور على مفتاح ${providerName} API. يرجى إعداده أولاً.`,
      'إعداد المفتاح'
    );

    if (action === 'إعداد المفتاح') {
      await vscode.commands.executeCommand('aiCoder.configure');
      return null;
    }
    return null;
  }

  return {
    apiKey,
    provider: config.get<string>('provider') || 'gemini',
    model: config.get<string>('model') || 'gemini-1.5-flash',
    maxTokens: config.get<number>('maxTokens') || 500,
    temperature: config.get<number>('temperature') || 0.7,
    language: config.get<string>('language') || 'arabic',
    showPreview: config.get<boolean>('showPreview') ?? true,
    autoSave: config.get<boolean>('autoSave') ?? false,
    includeContext: config.get<boolean>('includeContext') ?? true
  };
}

async function getUserPrompt(action: AIAction): Promise<string | undefined> {
  const prompts = {
    [AIAction.GENERATE]: 'ما الذي تود أن يولّده الذكاء الاصطناعي؟',
    [AIAction.EXPLAIN]: 'هل تريد شرح إضافي أو تفصيل معين؟ (اختياري)',
    [AIAction.IMPROVE]: 'ما نوع التحسين المطلوب؟ (الأداء، القراءة، الأمان، إلخ)',
    [AIAction.COMMENT]: 'نوع التعليقات المطلوبة؟ (وثائق، شرح، أمثلة)',
    [AIAction.DEBUG]: 'هل تواجه مشكلة معينة أو خطأ محدد؟',
    [AIAction.TEST]: 'نوع الاختبارات المطلوبة؟ (unit tests، integration tests)'
  };

  const placeholders = {
    [AIAction.GENERATE]: 'مثال: اكتب دالة لحساب المضروب',
    [AIAction.EXPLAIN]: 'مثال: اشرح بالتفصيل',
    [AIAction.IMPROVE]: 'مثال: حسن الأداء',
    [AIAction.COMMENT]: 'مثال: أضف تعليقات JSDoc',
    [AIAction.DEBUG]: 'مثال: هذا الكود لا يعمل كما متوقع',
    [AIAction.TEST]: 'مثال: اختبارات شاملة'
  };

  return await vscode.window.showInputBox({
    prompt: prompts[action],
    placeHolder: placeholders[action],
    ignoreFocusOut: true
  });
}

function getSystemPrompt(action: AIAction, language: string): string {
  const isArabic = language === 'arabic';
  const basePrompt = isArabic
    ? 'أنت مساعد برمجة ذكي ومتخصص. '
    : 'You are an intelligent and specialized programming assistant. ';

  const actionPrompts = {
    [AIAction.GENERATE]: isArabic
      ? 'قم بتوليد كود نظيف ومفهوم بناءً على طلب المستخدم. أضف تعليقات باللغة العربية عند الحاجة.'
      : 'Generate clean and understandable code based on user request. Add comments when needed.',

    [AIAction.EXPLAIN]: isArabic
      ? 'اشرح الكود المعطى بطريقة واضحة ومفصلة. اذكر الغرض من كل جزء وكيف يعمل.'
      : 'Explain the given code clearly and in detail. Mention the purpose of each part and how it works.',

    [AIAction.IMPROVE]: isArabic
      ? 'حلل الكود المعطى واقترح تحسينات في الأداء، القراءة، أو الأمان. اشرح سبب كل تحسين.'
      : 'Analyze the given code and suggest improvements in performance, readability, or security. Explain the reason for each improvement.',

    [AIAction.COMMENT]: isArabic
      ? 'أضف تعليقات مفيدة وواضحة للكود المعطى. استخدم أفضل الممارسات في التوثيق.'
      : 'Add useful and clear comments to the given code. Use best practices in documentation.',

    [AIAction.DEBUG]: isArabic
      ? 'فحص الكود المعطى للعثور على أخطاء محتملة أو مشاكل في المنطق. اقترح حلول.'
      : 'Examine the given code to find potential bugs or logic issues. Suggest solutions.',

    [AIAction.TEST]: isArabic
      ? 'اكتب اختبارات شاملة للكود المعطى. استخدم أفضل الممارسات في كتابة الاختبارات.'
      : 'Write comprehensive tests for the given code. Use best practices in test writing.'
  };

  return basePrompt + actionPrompts[action];
}

function buildFullPrompt(_action: AIAction, userPrompt: string, selectedText: string, languageId: string): string {
  let prompt = userPrompt;

  if (selectedText) {
    prompt += `\n\nالكود المطلوب العمل عليه (${languageId}):\n\`\`\`${languageId}\n${selectedText}\n\`\`\``;
  }

  return prompt;
}

async function processAIRequest(
  config: ExtensionConfig,
  systemPrompt: string,
  userPrompt: string,
  action: AIAction,
  editor: vscode.TextEditor
): Promise<void> {
  // وضع تجريبي للاختبار
  const DEMO_MODE = config.apiKey === 'demo' || config.apiKey === 'test';

  if (DEMO_MODE) {
    await handleDemoMode(action, userPrompt, editor, config);
    return;
  }

  // اختيار المزود المناسب
  if (config.provider === 'gemini') {
    await processGeminiRequest(config, systemPrompt, userPrompt, action, editor);
  } else {
    await processOpenAIRequest(config, systemPrompt, userPrompt, action, editor);
  }
}

async function processGeminiRequest(
  config: ExtensionConfig,
  systemPrompt: string,
  userPrompt: string,
  action: AIAction,
  editor: vscode.TextEditor
): Promise<void> {
  await vscode.window.withProgress({
    location: vscode.ProgressLocation.Notification,
    title: getProgressTitle(action),
    cancellable: false
  }, async (progress) => {
    try {
      progress.report({ increment: 30, message: 'الاتصال بـ Google Gemini...' });

      const fullPrompt = `${systemPrompt}\n\n${userPrompt}`;

      // تحديد النموذج الصحيح
      let modelName = config.model;
      if (modelName === 'gemini-pro') {
        modelName = 'gemini-1.5-flash'; // استخدام النموذج الجديد
      }

      const response = await fetch(`https://generativelanguage.googleapis.com/v1/models/${modelName}:generateContent?key=${config.apiKey}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: fullPrompt
            }]
          }],
          generationConfig: {
            temperature: config.temperature,
            maxOutputTokens: config.maxTokens,
          }
        })
      });

      progress.report({ increment: 50, message: 'معالجة الاستجابة...' });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        const errorMessage = errorData.error?.message || response.statusText;
        throw new Error(`خطأ من Gemini (${response.status}): ${errorMessage}`);
      }

      const data = await response.json();

      if (data.error) {
        throw new Error(`خطأ من Gemini: ${data.error.message}`);
      }

      const generated = data.candidates?.[0]?.content?.parts?.[0]?.text?.trim();

      if (generated) {
        progress.report({ increment: 20, message: 'معالجة النتيجة...' });
        await handleAIResponse(generated, action, editor, config);
      } else {
        vscode.window.showErrorMessage('لم يتم توليد أي محتوى من Gemini.');
      }
    } catch (error) {
      handleError(error);
    }
  });
}

async function processOpenAIRequest(
  config: ExtensionConfig,
  systemPrompt: string,
  userPrompt: string,
  action: AIAction,
  editor: vscode.TextEditor
): Promise<void> {
  await vscode.window.withProgress({
    location: vscode.ProgressLocation.Notification,
    title: getProgressTitle(action),
    cancellable: false
  }, async (progress) => {
    try {
      progress.report({ increment: 30, message: 'الاتصال بـ OpenAI...' });

      const response = await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${config.apiKey}`
        },
        body: JSON.stringify({
          model: config.model,
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: userPrompt }
          ],
          max_tokens: config.maxTokens,
          temperature: config.temperature
        })
      });

      progress.report({ increment: 50, message: 'معالجة الاستجابة...' });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        const errorMessage = errorData.error?.message || response.statusText;
        throw new Error(`خطأ من الخادم (${response.status}): ${errorMessage}`);
      }

      const data: OpenAIResponse = await response.json();

      if (data.error) {
        throw new Error(`خطأ من OpenAI: ${data.error.message}`);
      }

      const generated = data.choices?.[0]?.message?.content?.trim();

      if (generated) {
        progress.report({ increment: 20, message: 'معالجة النتيجة...' });
        await handleAIResponse(generated, action, editor, config);
      } else {
        vscode.window.showErrorMessage('لم يتم توليد أي محتوى.');
      }
    } catch (error) {
      handleError(error);
    }
  });
}

async function handleDemoMode(
  action: AIAction,
  userPrompt: string,
  editor: vscode.TextEditor,
  config: ExtensionConfig
): Promise<void> {
  await vscode.window.withProgress({
    location: vscode.ProgressLocation.Notification,
    title: `${getProgressTitle(action)} (وضع تجريبي)`,
    cancellable: false
  }, async (progress) => {
    progress.report({ increment: 50, message: 'توليد استجابة تجريبية...' });

    // تأخير قصير لمحاكاة API
    await new Promise(resolve => setTimeout(resolve, 1000));

    const demoResponses = {
      [AIAction.GENERATE]: `// دالة تجريبية مولدة بناءً على: ${userPrompt}
function demoFunction() {
    // هذا مثال تجريبي
    console.log("مرحبا من الوضع التجريبي!");
    return "تم توليد هذا الكود في الوضع التجريبي";
}`,

      [AIAction.EXPLAIN]: `## شرح الكود (وضع تجريبي)

**الطلب:** ${userPrompt}

هذا شرح تجريبي للكود المحدد. في الوضع الحقيقي، سيقوم الذكاء الاصطناعي بتحليل الكود وتقديم شرح مفصل لكل جزء.

**الوظائف الرئيسية:**
- تحليل بنية الكود
- شرح المنطق المستخدم
- توضيح الغرض من كل دالة`,

      [AIAction.IMPROVE]: `// كود محسن (وضع تجريبي)
// الطلب: ${userPrompt}

// هذا مثال على تحسين الكود
const improvedFunction = (data) => {
    // استخدام ES6+ features
    return data
        .filter(item => item != null && item > 0)
        .map(item => item * 2);
};`,

      [AIAction.COMMENT]: `/**
 * تعليقات تجريبية
 * الطلب: ${userPrompt}
 *
 * @description هذه دالة تجريبية مع تعليقات
 * @param {any} param - معامل تجريبي
 * @returns {any} نتيجة تجريبية
 */`,

      [AIAction.DEBUG]: `## تحليل الأخطاء (وضع تجريبي)

**الطلب:** ${userPrompt}

**المشاكل المحتملة:**
1. عدم فحص القيم الفارغة
2. عدم معالجة الاستثناءات
3. استخدام متغيرات غير محددة

**الحلول المقترحة:**
- إضافة فحص للقيم
- استخدام try-catch
- تحسين معالجة الأخطاء`,

      [AIAction.TEST]: `// اختبارات تجريبية
// الطلب: ${userPrompt}

describe('اختبارات تجريبية', () => {
    test('يجب أن يعمل الكود بشكل صحيح', () => {
        // هذا اختبار تجريبي
        expect(true).toBe(true);
    });

    test('اختبار الوظائف الأساسية', () => {
        // اختبارات إضافية
        expect(demoFunction()).toBeDefined();
    });
});`
    };

    const generated = demoResponses[action];
    progress.report({ increment: 50, message: 'إدراج النتيجة...' });

    await handleAIResponse(generated, action, editor, config);
  });
}

function getProgressTitle(action: AIAction): string {
  const titles = {
    [AIAction.GENERATE]: 'جاري توليد الكود...',
    [AIAction.EXPLAIN]: 'جاري شرح الكود...',
    [AIAction.IMPROVE]: 'جاري تحسين الكود...',
    [AIAction.COMMENT]: 'جاري إضافة التعليقات...',
    [AIAction.DEBUG]: 'جاري البحث عن الأخطاء...',
    [AIAction.TEST]: 'جاري توليد الاختبارات...'
  };
  return titles[action];
}

async function handleAIResponse(
  content: string,
  action: AIAction,
  editor: vscode.TextEditor,
  config: ExtensionConfig
): Promise<void> {
  if (action === AIAction.EXPLAIN || action === AIAction.DEBUG) {
    // عرض الشرح في نافذة منفصلة
    await showInformationPanel(content, action);
  } else {
    // للأكواد، عرض معاينة أو إدراج مباشر
    if (config.showPreview) {
      await showCodePreview(content, editor, config);
    } else {
      await insertCode(content, editor, config);
    }
  }
}

async function showInformationPanel(content: string, action: AIAction): Promise<void> {
  const title = action === AIAction.EXPLAIN ? 'شرح الكود' : 'تحليل الأخطاء';

  const panel = vscode.window.createWebviewPanel(
    'aiCoderInfo',
    title,
    vscode.ViewColumn.Beside,
    { enableScripts: true }
  );

  panel.webview.html = `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <style>
            body { font-family: Arial, sans-serif; padding: 20px; line-height: 1.6; }
            pre { background: #f4f4f4; padding: 10px; border-radius: 5px; overflow-x: auto; }
            code { background: #f4f4f4; padding: 2px 4px; border-radius: 3px; }
        </style>
    </head>
    <body>
        <h2>${title}</h2>
        <div>${content.replace(/\n/g, '<br>').replace(/```(.*?)```/gs, '<pre><code>$1</code></pre>')}</div>
    </body>
    </html>
  `;
}

async function showCodePreview(content: string, editor: vscode.TextEditor, config: ExtensionConfig): Promise<void> {
  const action = await vscode.window.showInformationMessage(
    'معاينة الكود المولد:',
    { modal: true, detail: content },
    'إدراج',
    'نسخ',
    'إلغاء'
  );

  switch (action) {
    case 'إدراج':
      await insertCode(content, editor, config);
      break;
    case 'نسخ':
      await vscode.env.clipboard.writeText(content);
      vscode.window.showInformationMessage('تم نسخ الكود! 📋');
      break;
  }
}

async function insertCode(content: string, editor: vscode.TextEditor, config: ExtensionConfig): Promise<void> {
  await editor.edit(editBuilder => {
    editBuilder.insert(editor.selection.active, content);
  });

  if (config.autoSave) {
    await editor.document.save();
  }

  vscode.window.showInformationMessage('تم إدراج الكود بنجاح! ✅');
}

async function testGeminiConnection(apiKey: string): Promise<void> {
  await vscode.window.withProgress({
    location: vscode.ProgressLocation.Notification,
    title: 'اختبار الاتصال مع Gemini...',
    cancellable: false
  }, async (progress) => {
    try {
      progress.report({ increment: 50, message: 'جاري الاتصال...' });

      // اختبار بسيط مع النماذج المتاحة
      const response = await fetch(`https://generativelanguage.googleapis.com/v1/models?key=${apiKey}`);

      if (!response.ok) {
        throw new Error(`خطأ في الاتصال: ${response.status}`);
      }

      const data = await response.json();
      const models = data.models?.map((m: any) => m.name) || [];

      progress.report({ increment: 50, message: 'تحليل النتائج...' });

      vscode.window.showInformationMessage(
        `✅ الاتصال ناجح! النماذج المتاحة: ${models.length}\n` +
        `أمثلة: ${models.slice(0, 3).join(', ')}`
      );

    } catch (error) {
      console.error('Gemini Connection Test Error:', error);
      vscode.window.showErrorMessage(`❌ فشل الاتصال: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`);
    }
  });
}

function handleError(error: unknown): void {
  console.error('AI Coder Error:', error);

  if (error instanceof Error) {
    if (error.message.includes('401') || error.message.includes('403')) {
      vscode.window.showErrorMessage('مفتاح API غير صحيح. يرجى التحقق من الإعدادات.');
    } else if (error.message.includes('429')) {
      vscode.window.showErrorMessage('تم تجاوز حد الاستخدام. يرجى المحاولة لاحقاً.');
    } else if (error.message.includes('404')) {
      vscode.window.showErrorMessage('النموذج غير موجود. جرب نموذج آخر في الإعدادات.');
    } else if (error.message.includes('network') || error.message.includes('fetch')) {
      vscode.window.showErrorMessage('خطأ في الاتصال بالإنترنت. يرجى التحقق من الاتصال.');
    } else {
      vscode.window.showErrorMessage(`خطأ: ${error.message}`);
    }
  } else {
    vscode.window.showErrorMessage('حدث خطأ غير متوقع.');
  }
}

export function deactivate() {
  console.log('AI Coder extension deactivated');
}
