import * as vscode from 'vscode';
import fetch from 'node-fetch';

interface OpenAIResponse {
  choices: Array<{
    message: {
      content: string;
    };
  }>;
  error?: {
    message: string;
    type: string;
  };
}

interface ExtensionConfig {
  apiKey: string;
  model: string;
  maxTokens: number;
  temperature: number;
}

export function activate(context: vscode.ExtensionContext) {
  console.log('AI Coder extension activated');

  // أمر مساعد للحصول على مفتاح API المحفوظ
  const getStoredApiKeyCommand = vscode.commands.registerCommand('aiCoder.getStoredApiKey', () => {
    return context.globalState.get<string>('aiCoder.apiKey');
  });

  const disposable = vscode.commands.registerCommand('aiCoder.generate', async () => {
    const editor = vscode.window.activeTextEditor;
    if (!editor) {
      vscode.window.showInformationMessage('لا يوجد ملف مفتوح.');
      return;
    }

    // الحصول على الإعدادات
    const config = await getConfiguration();
    if (!config) return;

    const userPrompt = await vscode.window.showInputBox({
      prompt: 'ما الذي تود أن يولّده الذكاء الاصطناعي؟',
      placeHolder: 'مثال: اكتب دالة لحساب المضروب',
      ignoreFocusOut: true
    });
    if (!userPrompt) return;

    // إظهار مؤشر التحميل
    await vscode.window.withProgress({
      location: vscode.ProgressLocation.Notification,
      title: 'جاري توليد الكود...',
      cancellable: false
    }, async (progress) => {
      try {
        progress.report({ increment: 30, message: 'الاتصال بـ OpenAI...' });

        const response = await fetch('https://api.openai.com/v1/chat/completions', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${config.apiKey}`
          },
          body: JSON.stringify({
            model: config.model,
            messages: [
              {
                role: 'system',
                content: 'أنت مساعد برمجة ذكي. قم بتوليد كود نظيف ومفهوم بناءً على طلب المستخدم. أضف تعليقات باللغة العربية عند الحاجة.'
              },
              {
                role: 'user',
                content: userPrompt
              }
            ],
            max_tokens: config.maxTokens,
            temperature: config.temperature
          })
        });

        progress.report({ increment: 50, message: 'معالجة الاستجابة...' });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          const errorMessage = errorData.error?.message || response.statusText;
          throw new Error(`خطأ من الخادم (${response.status}): ${errorMessage}`);
        }

        const data: OpenAIResponse = await response.json();

        if (data.error) {
          throw new Error(`خطأ من OpenAI: ${data.error.message}`);
        }

        const generated = data.choices?.[0]?.message?.content?.trim();

        progress.report({ increment: 20, message: 'إدراج الكود...' });

        if (generated) {
          await editor.edit(editBuilder => {
            editBuilder.insert(editor.selection.active, generated);
          });
          vscode.window.showInformationMessage('تم توليد الكود بنجاح! ✅');
        } else {
          vscode.window.showErrorMessage('لم يتم توليد أي كود.');
        }
      } catch (error) {
        console.error('AI Coder Error:', error);

        if (error instanceof Error) {
          if (error.message.includes('401')) {
            vscode.window.showErrorMessage('مفتاح API غير صحيح. يرجى التحقق من الإعدادات.');
          } else if (error.message.includes('429')) {
            vscode.window.showErrorMessage('تم تجاوز حد الاستخدام. يرجى المحاولة لاحقاً.');
          } else if (error.message.includes('network') || error.message.includes('fetch')) {
            vscode.window.showErrorMessage('خطأ في الاتصال بالإنترنت. يرجى التحقق من الاتصال.');
          } else {
            vscode.window.showErrorMessage(`خطأ: ${error.message}`);
          }
        } else {
          vscode.window.showErrorMessage('حدث خطأ غير متوقع أثناء توليد الكود.');
        }
      }
    });
  });

  // إضافة أمر لإعداد مفتاح API
  const configureCommand = vscode.commands.registerCommand('aiCoder.configure', async () => {
    const apiKey = await vscode.window.showInputBox({
      prompt: 'أدخل مفتاح OpenAI API الخاص بك',
      placeHolder: 'sk-...',
      password: true,
      ignoreFocusOut: true
    });

    if (apiKey) {
      await context.globalState.update('aiCoder.apiKey', apiKey);
      vscode.window.showInformationMessage('تم حفظ مفتاح API بنجاح! ✅');
    }
  });

  context.subscriptions.push(disposable, configureCommand, getStoredApiKeyCommand);
}

async function getConfiguration(): Promise<ExtensionConfig | null> {
  const config = vscode.workspace.getConfiguration('aiCoder');

  // الحصول على مفتاح API من التخزين الآمن
  let apiKey = await vscode.commands.executeCommand('aiCoder.getStoredApiKey') as string;

  if (!apiKey) {
    // محاولة الحصول على المفتاح من الإعدادات العامة (للتوافق مع الإصدارات القديمة)
    apiKey = config.get<string>('apiKey') || '';
  }

  if (!apiKey) {
    const action = await vscode.window.showErrorMessage(
      'لم يتم العثور على مفتاح OpenAI API. يرجى إعداده أولاً.',
      'إعداد المفتاح'
    );

    if (action === 'إعداد المفتاح') {
      await vscode.commands.executeCommand('aiCoder.configure');
      return null;
    }
    return null;
  }

  return {
    apiKey,
    model: config.get<string>('model') || 'gpt-3.5-turbo',
    maxTokens: config.get<number>('maxTokens') || 500,
    temperature: config.get<number>('temperature') || 0.7
  };
}

export function deactivate() {
  console.log('AI Coder extension deactivated');
}
